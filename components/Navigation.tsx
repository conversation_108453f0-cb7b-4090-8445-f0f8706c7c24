"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter, usePathname } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"
import { CorrespondenceIcon } from "@/components/CorrespondenceIcon"
import { CorrespondencePanel } from "@/components/CorrespondencePanel"
import { Day1Badge } from "@/components/Day1Badge"
import { useNavigation } from "@/contexts/NavigationContext"

export function Navigation() {
  const { isNavigationVisible } = useNavigation()
  const [user, setUser] = useState<{
    id: string;
    email: string;
    role: string;
    name?: string;
    avatar_url?: string;
    profile_picture_url?: string;
    has_day1_badge?: boolean;
    signup_number?: number;
    badge_tier?: string;
  } | null>(null)
  const [loading, setLoading] = useState(true) // Add loading state
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [userDropdownOpen, setUserDropdownOpen] = useState(false)
  const [correspondencePanelOpen, setCorrespondencePanelOpen] = useState(false)
  const [trendingDropdownOpen, setTrendingDropdownOpen] = useState(false)
  const [publishingDropdownOpen, setPublishingDropdownOpen] = useState(false)
  const [navigating, setNavigating] = useState<string | null>(null) // Track which button is loading
  const [signingOut, setSigningOut] = useState(false)
  const [activeNavItem, setActiveNavItem] = useState<string>('/write') // Track which nav item is active, default to create
  const router = useRouter()
  const pathname = usePathname()
  const supabase = createSupabaseClient()

  useEffect(() => {
    let mounted = true

    const fetchUser = async () => {
      setLoading(true)
      const { data: { session } } = await supabase.auth.getSession()
      if (mounted && session?.user) {
        const { data: profile } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single()
        setUser(profile || { id: session.user.id, email: session.user.email, role: 'writer' })
      } else {
        setUser(null)
      }
      if (mounted) {
        setLoading(false)
      }
    }

    fetchUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange(() => {
      if (mounted) {
        fetchUser()
      }
    })

    return () => {
      mounted = false
      subscription?.unsubscribe()
    }
  }, [supabase])


  const handleNavClick = (path: string) => {
    setNavigating(path)
    setActiveNavItem(path) // Set the clicked item as active
    setMobileMenuOpen(false) // Close mobile menu
    router.push(path)

    // Auto-reset loading after timeout in case navigation fails
    setTimeout(() => {
      setNavigating(null)
    }, 5000)
  }

  // Reset navigating state when pathname changes (navigation completes)
  useEffect(() => {
    setNavigating(null)
    // Update active nav item based on current pathname
    setActiveNavItem(pathname)
  }, [pathname])

  // Helper function to get button styling based on active state
  const getNavButtonStyle = (path: string) => {
    const isActive = activeNavItem === path
    if (isActive) {
      return "flex items-center gap-2 w-full text-left py-1.5 px-2.5 text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-lg transition-all duration-200 font-medium disabled:opacity-50 shadow-md"
    }
    return "flex items-center gap-2 w-full text-left py-1.5 px-2.5 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 font-medium disabled:opacity-50"
  }

  const getNavIconStyle = (path: string) => {
    const isActive = activeNavItem === path
    if (isActive) {
      return "w-5 h-5 bg-white/20 rounded-lg flex items-center justify-center"
    }

    // Different icon backgrounds for different sections
    switch (path) {
      case '/write':
        return "w-5 h-5 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-lg flex items-center justify-center"
      case '/dashboard':
        return "w-5 h-5 bg-gradient-to-br from-green-100 to-teal-100 rounded-lg flex items-center justify-center"
      case '/timeline':
        return "w-5 h-5 bg-gradient-to-br from-indigo-100 to-blue-100 rounded-lg flex items-center justify-center"
      case '/publishing':
        return "w-5 h-5 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-lg flex items-center justify-center"
      case '/changelog':
        return "w-5 h-5 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center"
      default:
        return "w-5 h-5 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center"
    }
  }

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.user-dropdown')) {
        setUserDropdownOpen(false)
      }
      if (!target.closest('.trending-dropdown')) {
        setTrendingDropdownOpen(false)
      }
      if (!target.closest('.publishing-dropdown')) {
        setPublishingDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [mobileMenuOpen])

  // Safety: Reset scroll on pathname change and ensure page is scrollable
  useEffect(() => {
    document.body.style.overflow = 'unset'
    document.documentElement.style.overflow = 'unset'
    setMobileMenuOpen(false)

    // Clear any focus that might prevent scrolling
    if (document.activeElement && document.activeElement !== document.body) {
      (document.activeElement as HTMLElement).blur()
    }

    // Ensure page is scrollable after navigation
    setTimeout(() => {
      document.body.style.overflow = 'unset'
      document.documentElement.style.overflow = 'unset'
    }, 100)
  }, [pathname])

  // Handle clicking outside dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (userDropdownOpen && !target.closest('.user-dropdown')) {
        setUserDropdownOpen(false)
      }
    }

    if (userDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [userDropdownOpen])

  const handleSignOut = async () => {
    try {
      console.log('Signing out...')

      // Clear auth state immediately
      await supabase.auth.signOut()

      // Clear any local storage
      localStorage.clear()
      sessionStorage.clear()

      console.log('Sign out complete, redirecting...')

      // Force immediate redirect
      window.location.replace('/')

    } catch (error) {
      console.error('Sign out error:', error)

      // Force clear everything and redirect anyway
      localStorage.clear()
      sessionStorage.clear()
      window.location.replace('/')
    }
  }

  const handleDropdownLinkClick = () => {
    setUserDropdownOpen(false)
  }

  // Show minimal navigation for e-reader mode
  if (!isNavigationVisible) {
    return (
      <>
        <nav className="bg-black/90 backdrop-blur-md border-b border-gray-800 sticky top-0 z-[60] shadow-lg">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-12">
            {/* Minimal Logo */}
            <div className="flex items-center">
              <Link href="/" className="group flex items-center gap-2 text-lg font-serif text-white hover:text-gray-300 transition-all duration-300">
                <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-md flex items-center justify-center group-hover:scale-105 transition-transform duration-300 shadow-sm">
                  <svg className="w-3.5 h-3.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                  </svg>
                </div>
                <span className="hidden sm:inline">OnlyDiary</span>
              </Link>
            </div>

            {/* E-Reader Navigation Controls */}
            <div className="flex items-center space-x-4">
              {!loading && user && (
                <div className="relative">
                  <button
                    onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                    className="flex items-center gap-2 text-white hover:text-gray-300 transition-colors duration-200 px-3 py-2 rounded-lg hover:bg-white/10"
                  >
                    {user.avatar_url || user.profile_picture_url ? (
                      <img
                        src={user.avatar_url || user.profile_picture_url}
                        alt={user.name || 'User avatar'}
                        className="w-7 h-7 rounded-full border-2 border-white/20"
                      />
                    ) : (
                      <div className="w-7 h-7 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full flex items-center justify-center border-2 border-white/20">
                        <span className="text-white text-sm">👤</span>
                      </div>
                    )}
                    <div className="hidden sm:flex items-center gap-2">
                      <span className="text-sm font-medium">{user.name}</span>
                      {user.has_day1_badge && (
                        <Day1Badge
                          signupNumber={user.signup_number}
                          badgeTier={user.badge_tier}
                          size="sm"
                          className="flex-shrink-0"
                        />
                      )}
                    </div>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {/* Enhanced E-Reader Dropdown */}
                  {userDropdownOpen && (
                    <div className="absolute right-0 top-full mt-2 w-72 bg-white rounded-xl shadow-2xl border border-gray-200 py-2 max-h-[80vh] overflow-y-auto" style={{ zIndex: 999999 }}>
                      {/* Exit Reader Section */}
                      <div className="px-4 py-3 border-b border-gray-100 bg-gradient-to-r from-red-50 to-pink-50">
                        <div className="flex items-center gap-2 text-red-700 mb-2">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                          <span className="font-semibold text-sm">Exit Reader</span>
                        </div>
                        <button
                          onClick={() => {
                            setUserDropdownOpen(false)
                            window.history.back()
                          }}
                          className="w-full text-left px-3 py-2 text-sm text-red-700 hover:bg-red-100 rounded-lg transition-colors font-medium"
                        >
                          ← Back to Book Page
                        </button>
                      </div>

                      {/* Navigation Section */}
                      <div className="px-2 py-2">
                        <button
                          onClick={() => {
                            setUserDropdownOpen(false)
                            handleNavClick('/books')
                          }}
                          disabled={navigating === '/books'}
                          className="w-full text-left flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                        >
                          {navigating === '/books' ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                              <span>📚</span>
                              <span>Books</span>
                            </>
                          ) : (
                            <>
                              <span>📚</span>
                              <span>Books</span>
                            </>
                          )}
                        </button>

                        <button
                          onClick={() => {
                            setUserDropdownOpen(false)
                            handleNavClick('/library')
                          }}
                          disabled={navigating === '/library'}
                          className="w-full text-left flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                        >
                          {navigating === '/library' ? (
                            <>
                              <div className="relative inline-block">
                                <span className="inline-block animate-pulse">📖</span>
                                <span className="absolute inset-0 animate-ping opacity-30">📄</span>
                              </div>
                              <span>My Library</span>
                              <span className="text-xs text-gray-500 animate-pulse">flipping...</span>
                            </>
                          ) : (
                            <>
                              <span>📖</span>
                              <span>My Library</span>
                            </>
                          )}
                        </button>

                        <button
                          onClick={() => {
                            setUserDropdownOpen(false)
                            handleNavClick('/discover')
                          }}
                          disabled={navigating === '/discover'}
                          className="w-full text-left flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                        >
                          {navigating === '/discover' ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                              <span>🔍</span>
                              <span>Explore Creators</span>
                            </>
                          ) : (
                            <>
                              <span>🔍</span>
                              <span>Explore Creators</span>
                            </>
                          )}
                        </button>

                        {/* Crown System - only for first 10K users */}
                        {user?.signup_number && user.signup_number <= 10000 && (
                          <button
                            onClick={() => {
                              setUserDropdownOpen(false)
                              handleNavClick('/crown-system')
                            }}
                            disabled={navigating === '/crown-system'}
                            className="w-full text-left flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                          >
                            {navigating === '/crown-system' ? (
                              <>
                                <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                                <span>👑</span>
                                <span>The Crown System</span>
                              </>
                            ) : (
                              <>
                                <span>👑</span>
                                <span>The Crown System</span>
                              </>
                            )}
                          </button>
                        )}

                        <button
                          onClick={() => {
                            setUserDropdownOpen(false)
                            handleNavClick('/dashboard')
                          }}
                          disabled={navigating === '/dashboard'}
                          className="w-full text-left flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                        >
                          {navigating === '/dashboard' ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                              <span>📊</span>
                              <span>Dashboard</span>
                            </>
                          ) : (
                            <>
                              <span>📊</span>
                              <span>Dashboard</span>
                            </>
                          )}
                        </button>
                      </div>

                      {/* Account Section */}
                      <div className="border-t border-gray-100 px-2 py-2">
                        {user?.role === 'admin' && (
                          <button
                            onClick={() => {
                              setUserDropdownOpen(false)
                              handleNavClick('/admin')
                            }}
                            className="w-full text-left flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                          >
                            <span>⚙️</span>
                            <span>Admin Panel</span>
                          </button>
                        )}

                        <button
                          onClick={() => {
                            setUserDropdownOpen(false)
                            handleNavClick(`/u/${user.id}`)
                          }}
                          disabled={navigating === `/u/${user.id}`}
                          className="w-full text-left flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                        >
                          {navigating === `/u/${user.id}` ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                              <span>👤</span>
                              <span>Profile</span>
                            </>
                          ) : (
                            <>
                              <span>👤</span>
                              <span>Profile</span>
                            </>
                          )}
                        </button>

                        <button
                          onClick={handleSignOut}
                          disabled={signingOut}
                          className="w-full text-left flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                        >
                          {signingOut ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                              <span>Signing out...</span>
                            </>
                          ) : (
                            <>
                              <span>🚪</span>
                              <span>Sign Out</span>
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Mobile menu button for e-reader */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="md:hidden text-white hover:text-gray-300 p-2 rounded-lg hover:bg-white/10 transition-all duration-200"
              >
                {mobileMenuOpen ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu for e-reader */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t border-gray-800">
            <div className="px-4 pt-4 pb-6 space-y-2 bg-black/95 backdrop-blur-md">
              {/* Exit Reader - Mobile */}
              <div className="mb-4 p-3 bg-gradient-to-r from-red-900/50 to-pink-900/50 rounded-lg border border-red-800/50">
                <div className="flex items-center gap-2 text-red-300 mb-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  <span className="font-semibold text-sm">Exit Reader</span>
                </div>
                <button
                  onClick={() => {
                    setMobileMenuOpen(false)
                    window.history.back()
                  }}
                  className="w-full text-left px-3 py-2 text-sm text-red-300 hover:bg-red-800/30 rounded-lg transition-colors font-medium"
                >
                  ← Back to Book Page
                </button>
              </div>

              {/* Quick Navigation - Mobile */}
              <button
                onClick={() => {
                  setMobileMenuOpen(false)
                  handleNavClick('/library')
                }}
                disabled={navigating === '/library'}
                className="flex items-center gap-3 w-full text-left py-3 px-4 text-white hover:bg-white/10 rounded-xl transition-all duration-200 disabled:opacity-70"
              >
                {navigating === '/library' ? (
                  <>
                    <div className="relative text-lg">
                      <span className="animate-pulse">📖</span>
                      <span className="absolute inset-0 animate-ping opacity-50">📄</span>
                    </div>
                    <span>My Library</span>
                    <span className="text-sm text-white/70 animate-pulse ml-auto">flipping pages...</span>
                  </>
                ) : (
                  <>
                    <span className="text-lg">📚</span>
                    <span>My Library</span>
                  </>
                )}
              </button>
              <button
                onClick={() => {
                  setMobileMenuOpen(false)
                  handleNavClick('/books')
                }}
                className="flex items-center gap-3 w-full text-left py-3 px-4 text-white hover:bg-white/10 rounded-xl transition-all duration-200"
              >
                <span className="text-lg">🔍</span>
                <span>Browse Books</span>
              </button>
              <button
                onClick={() => {
                  setMobileMenuOpen(false)
                  handleNavClick('/dashboard')
                }}
                className="flex items-center gap-3 w-full text-left py-3 px-4 text-white hover:bg-white/10 rounded-xl transition-all duration-200"
              >
                <span className="text-lg">📊</span>
                <span>Dashboard</span>
              </button>
              <button
                onClick={() => {
                  setMobileMenuOpen(false)
                  handleNavClick('/changelog')
                }}
                className="flex items-center gap-3 w-full text-left py-3 px-4 text-white hover:bg-white/10 rounded-xl transition-all duration-200"
              >
                <span className="text-lg">📝</span>
                <span>The Code Book</span>
              </button>
            </div>
          </div>
        )}
      </nav>
      </>
    )
  }

  return (
      <nav className="bg-white/95 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="group flex items-center gap-2 text-2xl font-serif text-gray-800 hover:text-gray-600 transition-all duration-300">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-300 shadow-sm">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                </svg>
              </div>
              <span className="bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                OnlyDiary
              </span>
            </Link>
          </div>

          <div className="hidden md:flex items-center space-x-6">
            {/* Trending Dropdown */}
            <div className="relative trending-dropdown">
              <button
                onClick={() => setTrendingDropdownOpen(!trendingDropdownOpen)}
                className="text-gray-600 hover:text-gray-900 font-medium cursor-pointer flex items-center gap-1 transition-colors duration-200 px-3 py-2 rounded-md hover:bg-gray-50"
              >
                Trending
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {trendingDropdownOpen && (
                <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <button
                    onClick={() => {
                      setTrendingDropdownOpen(false)
                      handleNavClick('/trending')
                    }}
                    disabled={navigating === '/trending'}
                    className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                  >
                    {navigating === '/trending' ? (
                      <>
                        <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                        <span>🔥 Trending Diary</span>
                      </>
                    ) : (
                      '🔥 Trending Diary'
                    )}
                  </button>
                  <button
                    onClick={() => {
                      setTrendingDropdownOpen(false)
                      handleNavClick('/trending/audio')
                    }}
                    disabled={navigating === '/trending/audio'}
                    className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                  >
                    {navigating === '/trending/audio' ? (
                      <>
                        <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                        <span>🎙️ Trending Audio</span>
                      </>
                    ) : (
                      '🎙️ Trending Audio'
                    )}
                  </button>
                  <button
                    onClick={() => {
                      setTrendingDropdownOpen(false)
                      handleNavClick('/bestsellers')
                    }}
                    disabled={navigating === '/bestsellers'}
                    className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                  >
                    {navigating === '/bestsellers' ? (
                      <>
                        <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                        <span>Bestsellers</span>
                      </>
                    ) : (
                      <>
                        <span>📈</span>
                        Bestsellers
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>

            {!loading && user ? (
              <>
                {/* Unified navigation - all users get all features */}
                <button
                  onClick={() => handleNavClick('/dashboard')}
                  disabled={navigating === '/dashboard'}
                  className="text-gray-600 hover:text-gray-900 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2 transition-colors duration-200 px-3 py-2 rounded-md hover:bg-gray-50"
                >
                  {navigating === '/dashboard' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                      <span>Dashboard</span>
                    </>
                  ) : (
                    'Dashboard'
                  )}
                </button>
                <button
                  onClick={() => handleNavClick('/write')}
                  disabled={navigating === '/write'}
                  className="text-gray-600 hover:text-gray-900 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2 transition-colors duration-200 px-3 py-2 rounded-md hover:bg-gray-50"
                  data-tutorial="create-button"
                >
                  {navigating === '/write' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                      <span>Create</span>
                    </>
                  ) : (
                    'Create'
                  )}
                </button>
                {/* Publishing Dropdown */}
                <div className="relative publishing-dropdown">
                  <button
                    onClick={() => setPublishingDropdownOpen(!publishingDropdownOpen)}
                    className="text-gray-600 hover:text-gray-900 font-medium cursor-pointer flex items-center gap-1 transition-colors duration-200 px-3 py-2 rounded-md hover:bg-gray-50"
                  >
                    Publishing
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {publishingDropdownOpen && (
                    <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                      <button
                        onClick={() => {
                          setPublishingDropdownOpen(false)
                          handleNavClick('/publishing')
                        }}
                        disabled={navigating === '/publishing'}
                        className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                      >
                        {navigating === '/publishing' ? (
                          <>
                            <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                            <span>Publishing Center</span>
                          </>
                        ) : (
                          <>
                            <span>📚</span>
                            Publishing Center
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => {
                          setPublishingDropdownOpen(false)
                          handleNavClick('/write/upload-ebook')
                        }}
                        disabled={navigating === '/write/upload-ebook'}
                        className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                      >
                        {navigating === '/write/upload-ebook' ? (
                          <>
                            <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                            <span>Upload Ebook</span>
                          </>
                        ) : (
                          <>
                            <span>📤</span>
                            Upload Ebook
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </div>
                <button
                  onClick={() => handleNavClick('/timeline')}
                  disabled={navigating === '/timeline'}
                  className="text-gray-600 hover:text-gray-900 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2 transition-colors duration-200 px-3 py-2 rounded-md hover:bg-gray-50"
                >
                  {navigating === '/timeline' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                      <span>Timeline</span>
                    </>
                  ) : (
                    'Timeline'
                  )}
                </button>

                <button
                  onClick={() => handleNavClick('/messages')}
                  disabled={navigating === '/messages'}
                  className="text-gray-600 hover:text-gray-900 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2 transition-colors duration-200 px-3 py-2 rounded-md hover:bg-gray-50"
                >
                  {navigating === '/messages' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                      <span>Messages</span>
                    </>
                  ) : (
                    'Messages'
                  )}
                </button>

                {/* Correspondence Icon */}
                <CorrespondenceIcon
                  userId={user.id}
                  onClick={() => setCorrespondencePanelOpen(true)}
                />

                <div className="relative user-dropdown">
                  <button
                    onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                    className="flex items-center gap-1 text-gray-600 hover:text-gray-800 font-medium"
                  >
                    {user.name || 'Profile'}
                    <svg
                      className={`w-4 h-4 transition-transform duration-200 ${userDropdownOpen ? 'rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                  {userDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-100 visible transition-all duration-200">
                    {/* Books - for browsing ebooks */}
                    <button
                      onClick={() => {
                        handleDropdownLinkClick()
                        handleNavClick('/books')
                      }}
                      disabled={navigating === '/books'}
                      className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                    >
                      {navigating === '/books' ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                          <span>📚</span>
                          Books
                        </>
                      ) : (
                        <>
                          <span>📚</span>
                          Books
                        </>
                      )}
                    </button>

                    {/* Library - for purchased books */}
                    <button
                      onClick={() => {
                        handleDropdownLinkClick()
                        handleNavClick('/library')
                      }}
                      disabled={navigating === '/library'}
                      className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                    >
                      {navigating === '/library' ? (
                        <>
                          <div className="relative inline-block">
                            <span className="animate-pulse text-amber-600">📖</span>
                            <span className="absolute inset-0 animate-ping opacity-40 text-amber-400">📄</span>
                          </div>
                          <span>My Library</span>
                          <span className="text-xs text-amber-600 animate-pulse">reading...</span>
                        </>
                      ) : (
                        <>
                          <span>📖</span>
                          My Library
                        </>
                      )}
                    </button>


                    {/* Discover - for finding new creators */}
                    <button
                      onClick={() => {
                        handleDropdownLinkClick()
                        handleNavClick('/discover')
                      }}
                      disabled={navigating === '/discover'}
                      className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                    >
                      {navigating === '/discover' ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                          <span>🔍</span>
                          Explore Creators
                        </>
                      ) : (
                        <>
                          <span>🔍</span>
                          Explore Creators
                        </>
                      )}
                    </button>

                    {/* The Code Book - for platform updates */}
                    <button
                      onClick={() => {
                        handleDropdownLinkClick()
                        handleNavClick('/changelog')
                      }}
                      disabled={navigating === '/changelog'}
                      className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                    >
                      {navigating === '/changelog' ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                          <span>📝</span>
                          The Code Book
                        </>
                      ) : (
                        <>
                          <span>📝</span>
                          The Code Book
                        </>
                      )}
                    </button>

                    {/* Crown System - only for first 10K users */}
                    {user?.signup_number && user.signup_number <= 10000 && (
                      <button
                        onClick={() => {
                          handleDropdownLinkClick()
                          handleNavClick('/crown-system')
                        }}
                        disabled={navigating === '/crown-system'}
                        className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                      >
                        {navigating === '/crown-system' ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                            <span>👑</span>
                            The Crown System
                          </>
                        ) : (
                          <>
                            <span>👑</span>
                            The Crown System
                          </>
                        )}
                      </button>
                    )}

                    <div className="border-t border-gray-100 my-1"></div>

                    {/* Admin Panel - only for admins */}
                    {user?.role === 'admin' && (
                      <button
                        onClick={() => {
                          handleDropdownLinkClick()
                          handleNavClick('/admin')
                        }}
                        disabled={navigating === '/admin'}
                        className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                      >
                        {navigating === '/admin' ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                            <span>⚙️</span>
                            Admin Panel
                          </>
                        ) : (
                          <>
                            <span>⚙️</span>
                            Admin Panel
                          </>
                        )}
                      </button>
                    )}

                    <button
                      onClick={() => {
                        handleDropdownLinkClick()
                        handleNavClick(`/u/${user.id}`)
                      }}
                      disabled={navigating === `/u/${user.id}`}
                      className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 disabled:opacity-50"
                    >
                      {navigating === `/u/${user.id}` ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                          <span>👤</span>
                          Profile
                        </>
                      ) : (
                        <>
                          <span>👤</span>
                          Profile
                        </>
                      )}
                    </button>

                    <button
                      onClick={handleSignOut}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                    >
                      <span>🚪</span>
                      Sign Out
                    </button>
                    </div>
                  )}
                </div>
              </>
            ) : !loading && (
              <>
                <button
                  onClick={() => handleNavClick('/login')}
                  disabled={navigating === '/login'}
                  className="text-gray-600 hover:text-gray-800 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2"
                >
                  {navigating === '/login' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                      <span>Sign In</span>
                    </>
                  ) : (
                    'Sign In'
                  )}
                </button>
                <button
                  onClick={() => handleNavClick('/register')}
                  disabled={navigating === '/register'}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors cursor-pointer disabled:opacity-50 flex items-center gap-2"
                >
                  {navigating === '/register' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Get Started</span>
                    </>
                  ) : (
                    'Get Started'
                  )}
                </button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center gap-2 relative z-40">
            {/* Mobile Correspondence Icon */}
            {user && (
              <CorrespondenceIcon
                userId={user.id}
                onClick={() => setCorrespondencePanelOpen(true)}
              />
            )}

            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                console.log('🔥 MOBILE MENU BUTTON CLICKED!', { mobileMenuOpen })
                setMobileMenuOpen(!mobileMenuOpen)
                // Force a visual feedback
                e.currentTarget.style.backgroundColor = '#ef4444'
                setTimeout(() => {
                  e.currentTarget.style.backgroundColor = ''
                }, 200)
              }}
              onTouchStart={(e) => {
                console.log('👆 TOUCH START on mobile menu button')
                e.currentTarget.style.backgroundColor = '#fef2f2'
              }}
              onTouchEnd={(e) => {
                console.log('👆 TOUCH END on mobile menu button')
                e.currentTarget.style.backgroundColor = ''
              }}
              className="text-gray-600 hover:text-gray-900 p-4 rounded-lg hover:bg-gray-100 transition-all duration-200 relative z-[9999] min-h-[56px] min-w-[56px] flex items-center justify-center border-2 border-red-500 bg-red-100"
              style={{
                WebkitTapHighlightColor: 'rgba(239, 68, 68, 0.3)',
                touchAction: 'manipulation',
                WebkitUserSelect: 'none',
                userSelect: 'none',
                position: 'relative',
                pointerEvents: 'auto'
              }}
              aria-label="Toggle mobile menu"
              type="button"
            >
              <div style={{ pointerEvents: 'none' }}>
                {mobileMenuOpen ? (
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ pointerEvents: 'none' }}>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ pointerEvents: 'none' }}>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                )}
              </div>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-3 pt-2 pb-3 bg-white border-t border-gray-200 shadow-lg animate-in slide-in-from-top-2 duration-300 max-h-[80vh] overflow-y-auto overscroll-contain">
              {/* Trending Section */}
              <div className="space-y-0.5 mb-2">
                <div className="flex items-center gap-2 px-2 py-0.5 mb-0.5">
                  <div className="w-1 h-2.5 bg-gradient-to-b from-orange-400 to-red-500 rounded-full"></div>
                  <span className="text-xs font-semibold text-gray-800 uppercase tracking-wide">Trending</span>
                </div>
                <button
                  onClick={() => handleNavClick('/trending')}
                  disabled={navigating === '/trending'}
                  className="flex items-center gap-2 w-full text-left py-1.5 px-2.5 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 font-medium disabled:opacity-50"
                >
                  <div className="w-5 h-5 bg-gradient-to-br from-orange-100 to-red-100 rounded-lg flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    {navigating === '/trending' ? (
                      <div className="flex items-center gap-1.5">
                        <div className="w-2.5 h-2.5 border-2 border-orange-600 border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-sm">Trending Diary</span>
                      </div>
                    ) : (
                      <span className="text-sm">Trending Diary</span>
                    )}
                  </div>
                </button>
                <button
                  onClick={() => handleNavClick('/trending/audio')}
                  disabled={navigating === '/trending/audio'}
                  className="flex items-center gap-2 w-full text-left py-1.5 px-2.5 text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 font-medium disabled:opacity-50 ml-2.5"
                >
                  <div className="w-4 h-4 bg-gradient-to-br from-purple-100 to-blue-100 rounded-lg flex items-center justify-center">
                    <svg className="w-2 h-2 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-1.594-.471-3.078-1.343-4.343a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 12a5.984 5.984 0 01-.757 2.828 1 1 0 11-1.415-1.414A3.987 3.987 0 0013 12a3.988 3.988 0 00-.172-1.172 1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    {navigating === '/trending/audio' ? (
                      <div className="flex items-center gap-1.5">
                        <div className="w-2.5 h-2.5 border-2 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-sm">Trending Audio</span>
                      </div>
                    ) : (
                      <span className="text-sm">Trending Audio</span>
                    )}
                  </div>
                </button>
                <button
                  onClick={() => handleNavClick('/bestsellers')}
                  disabled={navigating === '/bestsellers'}
                  className="flex items-center gap-2 w-full text-left py-1.5 px-2.5 text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 font-medium disabled:opacity-50 ml-2.5"
                >
                  <div className="w-4 h-4 bg-gradient-to-br from-green-100 to-emerald-100 rounded-lg flex items-center justify-center">
                    <svg className="w-2 h-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    {navigating === '/bestsellers' ? (
                      <div className="flex items-center gap-1.5">
                        <div className="w-2.5 h-2.5 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-sm">Bestsellers</span>
                      </div>
                    ) : (
                      <span className="text-sm">Bestsellers</span>
                    )}
                  </div>
                </button>
              </div>

              {user ? (
                <>
                  {/* Main Navigation Section */}
                  <div className="space-y-1 mb-3">
                    <div className="flex items-center gap-2 px-2 py-1 mb-1">
                      <div className="w-1 h-3 bg-gradient-to-b from-blue-400 to-purple-500 rounded-full"></div>
                      <span className="text-xs font-semibold text-gray-800 uppercase tracking-wide">Library</span>
                    </div>
                    <button
                      onClick={() => handleNavClick('/books')}
                      disabled={navigating === '/books'}
                      className="flex items-center gap-2 w-full text-left py-2 px-3 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 font-medium disabled:opacity-50"
                    >
                      <div className="w-6 h-6 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center">
                        <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        {navigating === '/books' ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin rounded-full h-3 w-3 border-2 border-blue-600 border-t-transparent"></div>
                            <span className="text-sm">All Books</span>
                          </div>
                        ) : (
                          <span className="text-sm">All Books</span>
                        )}
                      </div>
                    </button>
                    <button
                      onClick={() => handleNavClick('/library')}
                      disabled={navigating === '/library'}
                      className="flex items-center gap-2 w-full text-left py-2 px-3 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 font-medium disabled:opacity-50"
                    >
                      <div className={`w-6 h-6 bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg flex items-center justify-center ${navigating === '/library' ? 'animate-pulse' : ''}`}>
                        {navigating === '/library' ? (
                          <span className="text-sm animate-bounce">📚</span>
                        ) : (
                          <svg className="w-3 h-3 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15.586 13H14a1 1 0 01-1-1z" clipRule="evenodd" />
                        </svg>
                        )}
                      </div>
                      <div className="flex-1">
                        {navigating === '/library' ? (
                          <div className="flex items-center gap-2">
                            <span className="text-sm">My Library</span>
                            <span className="text-xs text-amber-600 animate-pulse">📄 turning pages...</span>
                          </div>
                        ) : (
                          <span className="text-sm">My Library</span>
                        )}
                      </div>
                    </button>
                  </div>

                  {/* Discovery Section */}
                  <div className="space-y-1 mb-3">
                    <div className="flex items-center gap-2 px-2 py-1 mb-1">
                      <div className="w-1 h-3 bg-gradient-to-b from-orange-400 to-red-500 rounded-full"></div>
                      <span className="text-xs font-semibold text-gray-800 uppercase tracking-wide">Discovery</span>
                    </div>
                    <button
                      onClick={() => handleNavClick('/discover')}
                      disabled={navigating === '/discover'}
                      className="flex items-center gap-2 w-full text-left py-2 px-3 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 font-medium disabled:opacity-50"
                    >
                      <div className="w-6 h-6 bg-gradient-to-br from-orange-100 to-red-100 rounded-lg flex items-center justify-center">
                        <svg className="w-3 h-3 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        {navigating === '/discover' ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin rounded-full h-3 w-3 border-2 border-orange-600 border-t-transparent"></div>
                            <span className="text-sm">Explore Creators</span>
                          </div>
                        ) : (
                          <span className="text-sm">Explore Creators</span>
                        )}
                      </div>
                    </button>
                    <button
                      onClick={() => handleNavClick('/changelog')}
                      disabled={navigating === '/changelog'}
                      className="flex items-center gap-2 w-full text-left py-2 px-3 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 font-medium disabled:opacity-50"
                    >
                      <div className="w-6 h-6 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center">
                        <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                          <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        {navigating === '/changelog' ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin rounded-full h-3 w-3 border-2 border-blue-600 border-t-transparent"></div>
                            <span className="text-sm">The Code Book</span>
                          </div>
                        ) : (
                          <span className="text-sm">The Code Book</span>
                        )}
                      </div>
                    </button>
                    <button
                      onClick={() => handleNavClick('/crown')}
                      disabled={navigating === '/crown'}
                      className="flex items-center gap-2 w-full text-left py-2 px-3 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 font-medium disabled:opacity-50"
                    >
                      <div className="w-6 h-6 bg-gradient-to-br from-yellow-100 to-amber-100 rounded-lg flex items-center justify-center">
                        <svg className="w-3 h-3 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        {navigating === '/crown' ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin rounded-full h-3 w-3 border-2 border-yellow-600 border-t-transparent"></div>
                            <span className="text-sm">The Crown System</span>
                          </div>
                        ) : (
                          <span className="text-sm">The Crown System</span>
                        )}
                      </div>
                    </button>
                  </div>

                  {/* Creator Tools Section */}
                  <div className="space-y-0.5 mb-2">
                    <div className="flex items-center gap-2 px-2 py-0.5 mb-0.5">
                      <div className="w-1 h-2.5 bg-gradient-to-b from-green-400 to-teal-500 rounded-full"></div>
                      <span className="text-xs font-semibold text-gray-800 uppercase tracking-wide">Creator Tools</span>
                    </div>
                    <button
                      onClick={() => handleNavClick('/dashboard')}
                      disabled={navigating === '/dashboard'}
                      className={getNavButtonStyle('/dashboard')}
                    >
                      <div className={getNavIconStyle('/dashboard')}>
                        <svg className={`w-2.5 h-2.5 ${activeNavItem === '/dashboard' ? 'text-white' : 'text-green-600'}`} fill="currentColor" viewBox="0 0 20 20">
                          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        {navigating === '/dashboard' ? (
                          <div className="flex items-center gap-1.5">
                            <div className={`animate-spin rounded-full h-2.5 w-2.5 border-2 ${activeNavItem === '/dashboard' ? 'border-white border-t-transparent' : 'border-green-600 border-t-transparent'}`}></div>
                            <span className="text-sm">Dashboard</span>
                          </div>
                        ) : (
                          <span className="text-sm">Dashboard</span>
                        )}
                      </div>
                    </button>
                    <button
                      onClick={() => handleNavClick('/timeline')}
                      disabled={navigating === '/timeline'}
                      className={getNavButtonStyle('/timeline')}
                    >
                      <div className={getNavIconStyle('/timeline')}>
                        <svg className={`w-2.5 h-2.5 ${activeNavItem === '/timeline' ? 'text-white' : 'text-indigo-600'}`} fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        {navigating === '/timeline' ? (
                          <div className="flex items-center gap-1.5">
                            <div className={`animate-spin rounded-full h-2.5 w-2.5 border-2 ${activeNavItem === '/timeline' ? 'border-white border-t-transparent' : 'border-indigo-600 border-t-transparent'}`}></div>
                            <span className="text-sm">Timeline</span>
                          </div>
                        ) : (
                          <span className="text-sm">Timeline</span>
                        )}
                      </div>
                    </button>
                    <button
                      onClick={() => handleNavClick('/write')}
                      disabled={navigating === '/write'}
                      className={getNavButtonStyle('/write')}
                    >
                      <div className={getNavIconStyle('/write')}>
                        <svg className={`w-2.5 h-2.5 ${activeNavItem === '/write' ? 'text-white' : 'text-purple-600'}`} fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        {navigating === '/write' ? (
                          <div className="flex items-center gap-1.5">
                            <div className={`animate-spin rounded-full h-2.5 w-2.5 border-2 ${activeNavItem === '/write' ? 'border-white border-t-transparent' : 'border-purple-600 border-t-transparent'}`}></div>
                            <span className="text-sm">CREATE</span>
                          </div>
                        ) : (
                          <span className="text-sm">CREATE</span>
                        )}
                      </div>
                    </button>
                  </div>
                  {/* Publishing Center Section */}
                  <div className="space-y-0.5 mb-2">
                    <div className="flex items-center gap-2 px-2 py-0.5 mb-0.5">
                      <div className="w-1 h-2.5 bg-gradient-to-b from-purple-400 to-indigo-500 rounded-full"></div>
                      <span className="text-xs font-semibold text-gray-800 uppercase tracking-wide">Publishing Center</span>
                    </div>
                    <button
                      onClick={() => handleNavClick('/publishing')}
                      disabled={navigating === '/publishing'}
                      className={getNavButtonStyle('/publishing')}
                    >
                      <div className={getNavIconStyle('/publishing')}>
                        <svg className={`w-2.5 h-2.5 ${activeNavItem === '/publishing' ? 'text-white' : 'text-purple-600'}`} fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                          <path fillRule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 1a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        {navigating === '/publishing' ? (
                          <div className="flex items-center gap-1.5">
                            <div className={`animate-spin rounded-full h-2.5 w-2.5 border-2 ${activeNavItem === '/publishing' ? 'border-white border-t-transparent' : 'border-purple-600 border-t-transparent'}`}></div>
                            <span className="text-sm">Publishing Center</span>
                          </div>
                        ) : (
                          <span className="text-sm">Publishing Center</span>
                        )}
                      </div>
                    </button>
                    <button
                      onClick={() => handleNavClick('/write/upload-ebook')}
                      disabled={navigating === '/write/upload-ebook'}
                      className="flex items-center gap-2 w-full text-left py-1.5 px-2.5 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 font-medium disabled:opacity-50"
                    >
                      <div className="w-5 h-5 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg flex items-center justify-center">
                        <svg className="w-2.5 h-2.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        {navigating === '/write/upload-ebook' ? (
                          <div className="flex items-center gap-1.5">
                            <div className="animate-spin rounded-full h-2.5 w-2.5 border-2 border-blue-600 border-t-transparent"></div>
                            <span className="text-sm">Edit E-Book</span>
                          </div>
                        ) : (
                          <span className="text-sm">Edit E-Book</span>
                        )}
                      </div>
                    </button>
                  </div>

                  <div className="border-t border-gray-200 my-2"></div>

                  {/* User Actions */}
                  <div className="space-y-1">
                    {/* Admin Panel - only for admins */}
                    {user?.role === 'admin' && (
                      <Link
                        href="/admin"
                        className="block py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium text-sm"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        ⚙️ Admin Panel
                      </Link>
                    )}

                    <button
                      onClick={() => {
                        setMobileMenuOpen(false)
                        handleNavClick(`/u/${user.id}`)
                      }}
                      disabled={navigating === `/u/${user.id}`}
                      className="w-full text-left block py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium text-sm disabled:opacity-50 flex items-center gap-2"
                    >
                      {navigating === `/u/${user.id}` ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent"></div>
                          👤 Profile
                        </>
                      ) : (
                        '👤 Profile'
                      )}
                    </button>
                    <button
                      onClick={() => {
                        handleSignOut()
                        setMobileMenuOpen(false)
                      }}
                      className="block w-full text-left py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium text-sm"
                    >
                      🚪 Sign Out
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="block py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium text-sm"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/register"
                    className="block py-2 px-3 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors font-medium text-sm"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Get Started
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
        </div>

        {/* Correspondence Panel */}
        {user && (
          <CorrespondencePanel
            isOpen={correspondencePanelOpen}
            onClose={() => setCorrespondencePanelOpen(false)}
            userId={user.id}
            onUnreadCountChange={(count) => {
              // This will trigger the CorrespondenceIcon to refetch its count
              // The icon component handles its own state, so we don't need to do anything here
            }}
          />
        )}
      </nav>
  )
}
