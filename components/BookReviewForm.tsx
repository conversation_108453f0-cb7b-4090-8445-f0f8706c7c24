"use client"

import { useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

interface BookReviewFormProps {
  bookId: string
  userId: string
  onReviewSubmitted: () => void
  onCancel: () => void
}

export function BookReviewForm({ bookId, userId, onReviewSubmitted, onCancel }: BookReviewFormProps) {
  const [rating, setRating] = useState(0)
  const [reviewText, setReviewText] = useState("")
  const [submitting, setSubmitting] = useState(false)
  const supabase = createSupabaseClient()

  const handleSubmit = async () => {
    if (rating === 0) {
      alert("Please select a rating")
      return
    }

    setSubmitting(true)
    try {
      // First, get the purchase ID for this user and book
      const { data: purchaseData, error: purchaseError } = await supabase
        .from('book_purchases')
        .select('id')
        .eq('user_id', userId)
        .eq('project_id', bookId)
        .single()

      if (purchaseError || !purchaseData) {
        throw new Error('Purchase record not found. Only verified purchasers can leave reviews.')
      }

      const { error } = await supabase
        .from('book_reviews')
        .insert({
          user_id: userId,
          project_id: bookId,
          purchase_id: purchaseData.id,
          pen_rating: rating,
          review_text: reviewText.trim() || null
        })

      if (error) throw error

      onReviewSubmitted()
    } catch (error) {
      console.error('Error submitting review:', error)
      alert('Failed to submit review. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Card className="border-purple-200 bg-purple-50/50">
      <CardContent className="p-6">
        <h3 className="text-lg font-serif text-gray-900 mb-4">
          📝 Write a Review
        </h3>
        
        <div className="space-y-4">
          {/* 10-Pen Rating System */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your Rating (1-10 pens)
            </label>
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((pen) => (
                <button
                  key={pen}
                  onClick={() => setRating(pen)}
                  className={`text-2xl transition-all hover:scale-110 ${
                    pen <= rating
                      ? 'text-yellow-600 drop-shadow-sm'
                      : 'text-gray-300 hover:text-yellow-400'
                  }`}
                  type="button"
                  style={{
                    filter: pen <= rating ? 'drop-shadow(0 1px 2px rgba(251, 191, 36, 0.3)) brightness(1.1) saturate(1.2)' : 'none'
                  }}
                >
                  {pen <= rating ? '🖊️' : '✒️'}
                </button>
              ))}
              <span className="ml-2 text-sm text-gray-600">
                {rating > 0 ? `${rating}/10 golden pens` : 'Select rating'}
              </span>
            </div>
          </div>

          {/* Review Text */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your Review (optional)
            </label>
            <textarea
              value={reviewText}
              onChange={(e) => setReviewText(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-24 resize-none"
              placeholder="Share your thoughts about this book..."
              maxLength={2000}
            />
            <p className="text-xs text-gray-500 mt-1">
              {reviewText.length}/2000 characters
            </p>
          </div>

          {/* Verified Purchase Notice */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center gap-2 text-green-800">
              <span className="text-sm">✅</span>
              <span className="text-sm font-medium">Verified Purchase</span>
            </div>
            <p className="text-xs text-green-700 mt-1">
              Only customers who have purchased this book can leave reviews
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-2">
            <Button
              onClick={onCancel}
              variant="outline"
              className="flex-1"
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              isLoading={submitting}
              disabled={rating === 0}
              className="flex-1 bg-purple-600 text-white hover:bg-purple-700"
            >
              {submitting ? 'Submitting...' : 'Submit Review'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface BookReviewDisplayProps {
  reviews: Array<{
    id: string
    pen_rating: number
    review_text: string | null
    created_at: string
    users: {
      name: string
      avatar?: string | null
      profile_picture_url?: string | null
    }
  }>
  averageRating: number
  totalReviews: number
  bookTitle?: string
  authorName?: string
}

export function BookReviewDisplay({ reviews, averageRating, totalReviews, bookTitle, authorName }: BookReviewDisplayProps) {
  const [expandedReviews, setExpandedReviews] = useState<Set<string>>(new Set())

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const toggleReviewExpansion = (reviewId: string) => {
    const newExpanded = new Set(expandedReviews)
    if (newExpanded.has(reviewId)) {
      newExpanded.delete(reviewId)
    } else {
      newExpanded.add(reviewId)
    }
    setExpandedReviews(newExpanded)
  }

  const truncateText = (text: string, maxLength: number = 200) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength).trim() + '...'
  }

  const renderSimplePenRating = (rating: number) => {
    if (rating === 0) return <span className="text-gray-400 text-xs">No rating</span>

    return (
      <div className="flex items-center gap-1 text-xs text-gray-600">
        <span
          className="text-yellow-600"
          style={{ filter: 'drop-shadow(0 1px 2px rgba(251, 191, 36, 0.3)) brightness(1.1) saturate(1.2)' }}
        >
          🖊️
        </span>
        <span className="font-medium">{rating.toFixed(1)}</span>
      </div>
    )
  }

  if (reviews.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-3">📝</div>
        <h3 className="text-lg font-serif text-gray-800 mb-2">No reviews yet</h3>
        <p className="text-gray-600">Be the first to review this book!</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Review Summary */}
      <div className="bg-gray-50 rounded-lg p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
          {/* Book Info */}
          <div className="flex-1">
            {bookTitle && (
              <h3 className="text-lg sm:text-xl font-serif text-gray-900 mb-1">
                {bookTitle}
              </h3>
            )}
            {authorName && (
              <p className="text-sm text-gray-600 mb-3">
                by {authorName}
              </p>
            )}

            {/* Rating Summary */}
            <div className="flex items-center gap-3">
              {renderSimplePenRating(averageRating)}
              <span className="text-sm text-gray-600">
                {averageRating.toFixed(1)} average • {totalReviews} review{totalReviews !== 1 ? 's' : ''}
              </span>
            </div>
          </div>

          {/* Full Rating Explainer */}
          <div className="flex-shrink-0">
            <div className="text-xs text-gray-500 mb-2">OnlyDiary uses a 10-pen rating system</div>
            <div className="flex items-center gap-0.5">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((pen) => (
                <span
                  key={pen}
                  className="text-xs text-yellow-600"
                  style={{
                    filter: 'drop-shadow(0 1px 2px rgba(251, 191, 36, 0.3)) brightness(1.1) saturate(1.2)'
                  }}
                >
                  🖊️
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Individual Reviews */}
      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id} className="border-gray-200 overflow-hidden">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-start gap-2 sm:gap-3">
                <div className="flex-1 min-w-0">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 gap-1 sm:gap-0">
                    <div className="min-w-0 flex items-center gap-2">
                      {/* Small profile picture beside name */}
                      <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                        {(review.users?.profile_picture_url || review.users?.avatar) ? (
                          <img
                            src={review.users.profile_picture_url || review.users.avatar}
                            alt={review.users?.name || 'User'}
                            className="w-6 h-6 rounded-full object-cover cursor-pointer hover:ring-2 hover:ring-purple-300 transition-all"
                            onClick={() => {
                              // Create modal for enlarged image
                              const modal = document.createElement('div')
                              modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4'
                              modal.onclick = () => modal.remove()

                              const img = document.createElement('img')
                              img.src = review.users.profile_picture_url || review.users.avatar
                              img.className = 'max-w-full max-h-full rounded-lg'
                              img.onclick = (e) => e.stopPropagation()

                              modal.appendChild(img)
                              document.body.appendChild(modal)
                            }}
                          />
                        ) : (
                          <span className="text-purple-600 font-medium text-xs">
                            {(review.users?.name || 'U').charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>

                      <div className="min-w-0">
                        <h4 className="font-medium text-gray-900 text-sm sm:text-base truncate">
                          {review.users?.name || 'Anonymous User'}
                        </h4>
                        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                          {renderSimplePenRating(review.pen_rating)}
                          <span className="text-xs text-green-600 whitespace-nowrap">✅ Verified Purchase</span>
                        </div>
                      </div>
                    </div>
                    <time className="text-xs text-gray-500 whitespace-nowrap">
                      {formatDate(review.created_at)}
                    </time>
                  </div>

                  {review.review_text && (
                    <div className="text-gray-700 leading-relaxed text-sm sm:text-base break-words">
                      <p>
                        {expandedReviews.has(review.id)
                          ? review.review_text
                          : truncateText(review.review_text)
                        }
                      </p>
                      {review.review_text.length > 200 && (
                        <button
                          onClick={() => toggleReviewExpansion(review.id)}
                          className="text-purple-600 hover:text-purple-700 text-sm font-medium mt-2 transition-colors"
                        >
                          {expandedReviews.has(review.id) ? 'Show less' : 'Show more'}
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
